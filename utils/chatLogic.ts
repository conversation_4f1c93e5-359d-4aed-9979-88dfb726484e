import { Bot, Message, BotResponse, ConversationContext } from '../types';
import { shouldBotRespondAndGenerate } from './openai';

export function extractMentions(content: string): string[] {
  const mentionRegex = /@(\w+)/g;
  const mentions = [];
  let match;
  
  while ((match = mentionRegex.exec(content)) !== null) {
    mentions.push(match[1]);
  }
  
  return mentions;
}

export function getConversationContext(messages: Message[], lookbackCount: number = 15): ConversationContext {
  const recentMessages = messages.slice(-lookbackCount);
  const mentionedBots = recentMessages.flatMap(msg => msg.mentions || []);
  
  // Simple topic extraction based on recent messages
  const conversationTopic = recentMessages
    .map(msg => msg.content)
    .join(' ')
    .toLowerCase();
  
  // Calculate user engagement based on recent activity
  const userMessages = recentMessages.filter(msg => msg.type === 'user');
  const userEngagement = userMessages.length / Math.max(recentMessages.length, 1);
  
  return {
    recentMessages,
    mentionedBots,
    conversationTopic,
    userEngagement
  };
}

export async function calculateBotResponses(
  bots: Bot[],
  context: ConversationContext,
  lastMessage: Message
): Promise<Map<string, BotResponse & { response?: string }>> {
  const responses = new Map<string, BotResponse & { response?: string }>();

  // Get mentions from the last message
  const mentions = lastMessage.mentions || [];

  // Prepare conversation history for AI analysis
  const conversationHistory = context.recentMessages
    .map(msg => `${msg.senderName}: ${msg.content}`)
    .join('\n');

  for (const bot of bots) {
    const wasMentioned = mentions.includes(bot.name.toLowerCase()) || mentions.includes(bot.id);

    // Skip if this bot sent the last message (prevent immediate self-response)
    if (lastMessage.sender === bot.id) {
      responses.set(bot.id, { shouldRespond: false, delay: 0, priority: 0 });
      continue;
    }

    // Additional check: Skip if this bot sent any of the last 3 messages (extra safety)
    const lastThreeMessages = context.recentMessages.slice(-3);
    const sentRecentMessage = lastThreeMessages.some(msg => msg.sender === bot.id);
    if (sentRecentMessage && !wasMentioned) {
      responses.set(bot.id, { shouldRespond: false, delay: 0, priority: 0 });
      continue;
    }

    // For bot messages, be more selective about responses to prevent spam
    const isResponseToBotMessage = lastMessage.type === 'bot';
    const timeSinceLastResponse = Date.now() - bot.lastActiveTime;

    // Adjust cooldown based on message type
    const cooldownTime = isResponseToBotMessage ? 5000 : 2000; // Longer cooldown for bot-to-bot

    if (timeSinceLastResponse < cooldownTime && !wasMentioned) {
      responses.set(bot.id, { shouldRespond: false, delay: 0, priority: 0 });
      continue;
    }

    try {
      const { shouldRespond, response, priority } = await shouldBotRespondAndGenerate(
        bot.persona,
        bot.knowledgeDomain,
        conversationHistory,
        wasMentioned,
        bot.model
      );

      if (shouldRespond && response) {
        // Calculate response delay based on bot's response style and priority
        let baseDelay = getBaseDelay(bot.responseStyle);

        // Add more randomness for bot-to-bot conversations
        const randomFactor = isResponseToBotMessage ? 0.7 + Math.random() * 0.8 : 0.5 + Math.random();
        const delay = Math.floor(baseDelay * randomFactor);

        // Mentioned bots respond faster, but still with some delay for bot messages
        let finalDelay = wasMentioned ? Math.min(delay, 1000) : delay;
        if (isResponseToBotMessage && !wasMentioned) {
          finalDelay = Math.max(finalDelay, 3000); // Minimum 3s delay for bot-to-bot
        }

        responses.set(bot.id, {
          shouldRespond: true,
          delay: finalDelay,
          priority: wasMentioned ? priority + 5 : priority,
          response: response
        });
      } else {
        responses.set(bot.id, { shouldRespond: false, delay: 0, priority: 0 });
      }
    } catch (error) {
      console.error(`Error calculating response for bot ${bot.id}:`, error);
      responses.set(bot.id, { shouldRespond: false, delay: 0, priority: 0 });
    }
  }

  return responses;
}

function getBaseDelay(responseStyle: Bot['responseStyle']): number {
  switch (responseStyle) {
    case 'quick':
      return 1000 + Math.random() * 2000; // 1-3 seconds
    case 'thoughtful':
      return 3000 + Math.random() * 4000; // 3-7 seconds
    case 'analytical':
      return 5000 + Math.random() * 5000; // 5-10 seconds
    default:
      return 2000 + Math.random() * 3000; // 2-5 seconds
  }
}

export function formatConversationForBot(messages: Message[], _botId: string): string {
  return messages
    .slice(-10) // Last 10 messages for context
    .map(msg => {
      const prefix = msg.type === 'user' ? 'User' : msg.senderName;
      return `${prefix}: ${msg.content}`;
    })
    .join('\n');
}

export function shouldLimitBotActivity(messages: Message[], timeWindow: number = 30000): boolean {
  const now = Date.now();
  const recentMessages = messages.filter(msg => (now - msg.timestamp) < timeWindow);
  const recentBotMessages = recentMessages.filter(msg => msg.type === 'bot');
  const recentUserMessages = recentMessages.filter(msg => msg.type === 'user');

  // If there are recent user messages, allow more bot activity
  if (recentUserMessages.length > 0) {
    // Allow up to 6 bot messages in 30 seconds when users are active
    return recentBotMessages.length >= 6;
  }

  // If no recent user activity, limit bot-to-bot conversations more strictly
  // Allow max 3 consecutive bot messages without user input
  const lastThreeMessages = messages.slice(-3);
  const allBotsInLastThree = lastThreeMessages.every(msg => msg.type === 'bot');

  return allBotsInLastThree && lastThreeMessages.length >= 3;
}
