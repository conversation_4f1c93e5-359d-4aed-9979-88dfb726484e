import { Bot, Message, BotResponse, ConversationContext } from '../types';
import { shouldBotRespond } from './openai';

export function extractMentions(content: string): string[] {
  const mentionRegex = /@(\w+)/g;
  const mentions = [];
  let match;
  
  while ((match = mentionRegex.exec(content)) !== null) {
    mentions.push(match[1]);
  }
  
  return mentions;
}

export function getConversationContext(messages: Message[], lookbackCount: number = 15): ConversationContext {
  const recentMessages = messages.slice(-lookbackCount);
  const mentionedBots = recentMessages.flatMap(msg => msg.mentions || []);
  
  // Simple topic extraction based on recent messages
  const conversationTopic = recentMessages
    .map(msg => msg.content)
    .join(' ')
    .toLowerCase();
  
  // Calculate user engagement based on recent activity
  const userMessages = recentMessages.filter(msg => msg.type === 'user');
  const userEngagement = userMessages.length / Math.max(recentMessages.length, 1);
  
  return {
    recentMessages,
    mentionedBots,
    conversationTopic,
    userEngagement
  };
}

export async function calculateBotResponses(
  bots: Bot[],
  context: ConversationContext,
  lastMessage: Message
): Promise<Map<string, BotResponse>> {
  const responses = new Map<string, BotResponse>();
  
  // Get mentions from the last message
  const mentions = lastMessage.mentions || [];
  
  // Prepare conversation history for AI analysis
  const conversationHistory = context.recentMessages
    .map(msg => `${msg.senderName}: ${msg.content}`)
    .join('\n');
  
  for (const bot of bots) {
    const wasMentioned = mentions.includes(bot.name.toLowerCase()) || mentions.includes(bot.id);
    
    // Skip if bot just responded recently (prevent spam)
    const timeSinceLastResponse = Date.now() - bot.lastActiveTime;
    if (timeSinceLastResponse < 2000 && !wasMentioned) {
      responses.set(bot.id, { shouldRespond: false, delay: 0, priority: 0 });
      continue;
    }
    
    try {
      const { shouldRespond, priority } = await shouldBotRespond(
        bot.persona,
        bot.knowledgeDomain,
        conversationHistory,
        wasMentioned
      );
      
      if (shouldRespond) {
        // Calculate response delay based on bot's response style and priority
        let baseDelay = getBaseDelay(bot.responseStyle);
        
        // Add some randomness to make it feel natural
        const randomFactor = 0.5 + Math.random();
        const delay = Math.floor(baseDelay * randomFactor);
        
        // Mentioned bots respond faster
        const finalDelay = wasMentioned ? Math.min(delay, 1000) : delay;
        
        responses.set(bot.id, {
          shouldRespond: true,
          delay: finalDelay,
          priority: wasMentioned ? priority + 5 : priority
        });
      } else {
        responses.set(bot.id, { shouldRespond: false, delay: 0, priority: 0 });
      }
    } catch (error) {
      console.error(`Error calculating response for bot ${bot.id}:`, error);
      responses.set(bot.id, { shouldRespond: false, delay: 0, priority: 0 });
    }
  }
  
  return responses;
}

function getBaseDelay(responseStyle: Bot['responseStyle']): number {
  switch (responseStyle) {
    case 'quick':
      return 1000 + Math.random() * 2000; // 1-3 seconds
    case 'thoughtful':
      return 3000 + Math.random() * 4000; // 3-7 seconds
    case 'analytical':
      return 5000 + Math.random() * 5000; // 5-10 seconds
    default:
      return 2000 + Math.random() * 3000; // 2-5 seconds
  }
}

export function formatConversationForBot(messages: Message[], botId: string): string {
  return messages
    .slice(-10) // Last 10 messages for context
    .map(msg => {
      const prefix = msg.type === 'user' ? 'User' : msg.senderName;
      return `${prefix}: ${msg.content}`;
    })
    .join('\n');
}

export function shouldLimitBotActivity(messages: Message[], timeWindow: number = 30000): boolean {
  const now = Date.now();
  const recentBotMessages = messages.filter(
    msg => msg.type === 'bot' && (now - msg.timestamp) < timeWindow
  );
  
  // Limit to max 5 bot messages in 30 seconds to allow user participation
  return recentBotMessages.length >= 5;
}
