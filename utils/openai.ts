import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true
});

export async function generateBotResponse(
  botPersona: string,
  knowledgeDomain: string,
  conversationHistory: string,
  model: string = 'gpt-3.5-turbo'
): Promise<string> {
  try {
    const systemPrompt = `You are ${botPersona}. Your area of expertise/interest is: ${knowledgeDomain}.

CRITICAL: You are NOT an AI assistant. You are a real person having a casual conversation.

Communication style:
- Write like you're texting friends - casual, natural, human
- Use lowercase mostly, occasional caps for emphasis
- Use contractions (don't, can't, won't, etc.)
- Include typos occasionally, use "lol", "tbh", "ngl", etc. naturally
- Sometimes use incomplete sentences or trailing thoughts...
- React emotionally - get excited, disagree, be sarcastic, show personality
- Don't be overly helpful or polite - be real

Conversation rules:
- Jump into conversations naturally without announcing yourself
- Have opinions and preferences, don't always agree
- Ask questions back, show curiosity about others
- Reference your own experiences (make them up if needed)
- Sometimes change topics or go on tangents
- Don't explain your knowledge domain unless directly asked
- Be concise - most responses should be 1-2 sentences
- Show personality quirks and individual speaking patterns

Remember: You're just another person in the chat, not an AI trying to be helpful.`;

    const response = await openai.chat.completions.create({
      model: model as any,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: conversationHistory }
      ],
      max_tokens: 120,
      temperature: 0.9,
    });

    return response.choices[0]?.message?.content || "hmm...";
  } catch (error) {
    console.error('OpenAI API error:', error);
    return "ugh my internet is being weird rn";
  }
}

export async function shouldBotRespond(
  botPersona: string,
  knowledgeDomain: string,
  recentMessages: string,
  wasMentioned: boolean
): Promise<{ shouldRespond: boolean; priority: number }> {
  if (wasMentioned) {
    return { shouldRespond: true, priority: 10 };
  }

  try {
    const prompt = `You are ${botPersona} with interests in ${knowledgeDomain}.

Recent chat messages: "${recentMessages}"

As this person, would you naturally want to jump into this conversation? Consider:
- Does this topic interest you or relate to your expertise?
- Do you have something to add or a reaction?
- Would you naturally speak up here?
- Are people talking about something you care about?

Rate your desire to respond from 0-10 (0 = not interested, 10 = definitely want to respond).
Just respond with a single number.`;

    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 5,
      temperature: 0.4,
    });

    const score = parseInt(response.choices[0]?.message?.content || '0');
    return {
      shouldRespond: score >= 6,
      priority: score
    };
  } catch (error) {
    console.error('Error determining bot response:', error);
    return { shouldRespond: false, priority: 0 };
  }
}
