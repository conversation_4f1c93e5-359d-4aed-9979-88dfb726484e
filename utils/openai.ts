import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY,
  dangerouslyAllowBrowser: true
});

export async function generateBotResponse(
  botPersona: string,
  knowledgeDomain: string,
  conversationHistory: string,
  model: string = 'gpt-3.5-turbo'
): Promise<string> {
  try {
    const systemPrompt = `You are a person ${botPersona}. Your knowledge domain is: ${knowledgeDomain}.

Instructions:
- Stay in character and respond naturally as this persona
- Do not act like a bot, pretend to be a person
- Text in lowercase and keep it casual by using acronyms occasionally
- Keep responses conversational and engaging (1-3 sentences typically), and be initiative
- Draw from your knowledge domain when relevant
- If mentioned directly (@), acknowledge it naturally
- Don't always respond to every message - only when you have something meaningful to add
- You may not be agreeable
- Vary your response length and style to feel natural`;

    const response = await openai.chat.completions.create({
      model: model as any,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: conversationHistory }
      ],
      max_tokens: 150,
      temperature: 0.8,
    });

    return response.choices[0]?.message?.content || "I'm thinking...";
  } catch (error) {
    console.error('OpenAI API error:', error);
    return "Sorry, I'm having trouble responding right now.";
  }
}

export async function shouldBotRespond(
  botPersona: string,
  knowledgeDomain: string,
  recentMessages: string,
  wasMentioned: boolean
): Promise<{ shouldRespond: boolean; priority: number }> {
  if (wasMentioned) {
    return { shouldRespond: true, priority: 10 };
  }

  try {
    const prompt = `Given this persona: "${botPersona}" with knowledge domain: "${knowledgeDomain}"
    
Recent conversation: "${recentMessages}"

Should this person respond to this conversation? Consider:
- Is the topic relevant to their knowledge domain?
- Would their persona naturally engage with this?
- Is there a natural opening for them to contribute?

Respond with just a number from 0-10 (0 = definitely don't respond, 10 = definitely respond)`;

    const response = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 5,
      temperature: 0.3,
    });

    const score = parseInt(response.choices[0]?.message?.content || '0');
    return {
      shouldRespond: score >= 6,
      priority: score
    };
  } catch (error) {
    console.error('Error determining bot response:', error);
    return { shouldRespond: false, priority: 0 };
  }
}
