export interface Bot {
  id: string;
  name: string;
  model: 'gpt-4' | 'gpt-3.5-turbo' | 'gpt-4-turbo';
  knowledgeDomain: string;
  persona: string;
  avatar: string;
  color: string;
  isTyping: boolean;
  lastActiveTime: number;
  responseStyle: 'quick' | 'thoughtful' | 'analytical';
}

export interface Message {
  id: string;
  content: string;
  sender: string; // bot id or 'user'
  senderName: string;
  timestamp: number;
  type: 'user' | 'bot';
  mentions?: string[]; // bot ids that were mentioned
  isEdited?: boolean;
  reactions?: Reaction[];
}

export interface Reaction {
  emoji: string;
  count: number;
  users: string[];
}

export interface ChatState {
  messages: Message[];
  bots: Bot[];
  activeUsers: string[];
  isUserTyping: boolean;
}

export interface BotResponse {
  shouldRespond: boolean;
  delay: number; // milliseconds to wait before responding
  priority: number; // higher priority bots respond first
}

export interface ConversationContext {
  recentMessages: Message[];
  mentionedBots: string[];
  conversationTopic: string;
  userEngagement: number;
}
