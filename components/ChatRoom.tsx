'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { v4 as uuidv4 } from 'uuid';
import { Bot, Message, ChatState } from '../types';
import { 
  extractMentions, 
  getConversationContext, 
  calculateBotResponses,
  shouldLimitBotActivity 
} from '../utils/chatLogic';
import MessageBubble from './MessageBubble';
import TypingIndicator from './TypingIndicator';
import UserInput from './UserInput';
import BotManager from './BotManager';
import BotCard from './BotCard';

export default function ChatRoom() {
  const [chatState, setChatState] = useState<ChatState>({
    messages: [],
    bots: [],
    activeUsers: ['user'],
    isUserTyping: false
  });
  
  const [typingBots, setTypingBots] = useState<Set<string>>(new Set());
  const [showBotManager, setShowBotManager] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const responseTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [chatState.messages, typingBots, scrollToBottom]);

  // Load bots from API on component mount
  useEffect(() => {
    loadBots();
  }, []);

  const loadBots = async () => {
    try {
      const response = await fetch('/api/bots');
      const data = await response.json();
      setChatState(prev => ({ ...prev, bots: data.bots || [] }));
    } catch (error) {
      console.error('Failed to load bots:', error);
    }
  };

  const addBot = async (botData: Partial<Bot>) => {
    try {
      const response = await fetch('/api/bots', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(botData)
      });
      
      const data = await response.json();
      if (data.bot) {
        setChatState(prev => ({
          ...prev,
          bots: [...prev.bots, data.bot]
        }));
        
        // Add welcome message from the new bot
        setTimeout(() => {
          addMessage({
            content: `Hello! I'm ${data.bot.name}. I specialize in ${data.bot.knowledgeDomain}. Feel free to ask me anything or mention me with @${data.bot.name}!`,
            sender: data.bot.id,
            senderName: data.bot.name,
            type: 'bot'
          });
        }, 1000);
      }
    } catch (error) {
      console.error('Failed to add bot:', error);
    }
  };

  const deleteBot = async (botId: string) => {
    try {
      await fetch(`/api/bots?id=${botId}`, { method: 'DELETE' });
      setChatState(prev => ({
        ...prev,
        bots: prev.bots.filter(bot => bot.id !== botId)
      }));
      
      // Clear any pending responses from this bot
      const timeout = responseTimeouts.current.get(botId);
      if (timeout) {
        clearTimeout(timeout);
        responseTimeouts.current.delete(botId);
      }
      
      setTypingBots(prev => {
        const newSet = new Set(prev);
        newSet.delete(botId);
        return newSet;
      });
    } catch (error) {
      console.error('Failed to delete bot:', error);
    }
  };
  
  const addMessage = (messageData: Partial<Message>) => {
    const message: Message = {
      id: uuidv4(),
      content: messageData.content || '',
      sender: messageData.sender || 'user',
      senderName: messageData.senderName || 'User',
      timestamp: Date.now(),
      type: messageData.type || 'user',
      mentions: messageData.mentions || [],
      ...messageData
    };

    setChatState(prev => ({
      ...prev,
      messages: [...prev.messages, message]
    }));

    return message;
  };

  const handleUserMessage = async (content: string, mentions: string[]) => {
    // Add user message
    const userMessage = addMessage({
      content,
      sender: 'user',
      senderName: 'User',
      type: 'user',
      mentions
    });

    // Check if we should limit bot activity
    if (shouldLimitBotActivity(chatState.messages)) {
      return;
    }

    // Get conversation context
    const context = getConversationContext([...chatState.messages, userMessage]);
    
    // Calculate which bots should respond
    const botResponses = await calculateBotResponses(chatState.bots, context, userMessage);
    
    // Schedule bot responses
    botResponses.forEach((response, botId) => {
      if (response.shouldRespond) {
        scheduleBotResponse(botId, response.delay);
      }
    });
  };

  const scheduleBotResponse = (botId: string, delay: number) => {
    const bot = chatState.bots.find(b => b.id === botId);
    if (!bot) return;

    // Clear any existing timeout for this bot
    const existingTimeout = responseTimeouts.current.get(botId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Show typing indicator
    setTypingBots(prev => new Set([...prev, botId]));

    // Schedule the response
    const timeout = setTimeout(async () => {
      try {
        const response = await fetch('/api/chat', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            bot,
            messages: chatState.messages
          })
        });

        const data = await response.json();
        
        // Remove typing indicator
        setTypingBots(prev => {
          const newSet = new Set(prev);
          newSet.delete(botId);
          return newSet;
        });

        // Add bot response
        if (data.response) {
          addMessage({
            content: data.response,
            sender: botId,
            senderName: bot.name,
            type: 'bot'
          });

          // Update bot's last active time
          setChatState(prev => ({
            ...prev,
            bots: prev.bots.map(b => 
              b.id === botId 
                ? { ...b, lastActiveTime: Date.now() }
                : b
            )
          }));
        }
      } catch (error) {
        console.error(`Failed to get response from bot ${botId}:`, error);
        setTypingBots(prev => {
          const newSet = new Set(prev);
          newSet.delete(botId);
          return newSet;
        });
      } finally {
        responseTimeouts.current.delete(botId);
      }
    }, delay);

    responseTimeouts.current.set(botId, timeout);
  };

  const mentionBot = (botName: string) => {
    // This will be handled by the UserInput component
    // We can add logic here if needed
  };

  const getBotById = (botId: string) => {
    return chatState.bots.find(bot => bot.id === botId);
  };

  return (
    <div className="flex h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Sidebar */}
      <motion.div
        initial={{ x: -300 }}
        animate={{ x: 0 }}
        className="w-80 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-r border-gray-200/50 dark:border-gray-700/50 p-4 overflow-y-auto"
      >
        <BotManager
          bots={chatState.bots}
          onAddBot={addBot}
          onDeleteBot={deleteBot}
          onMentionBot={mentionBot}
        />
      </motion.div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <motion.div
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-b border-gray-200/50 dark:border-gray-700/50 p-4"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                AI Chatroom
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {chatState.bots.length} bots • {chatState.messages.length} messages
              </p>
            </div>
            
            {/* Active Bots */}
            <div className="flex items-center gap-2">
              {chatState.bots.slice(0, 5).map((bot) => (
                <div
                  key={bot.id}
                  className="relative"
                  title={bot.name}
                >
                  <div 
                    className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                    style={{ backgroundColor: bot.color }}
                  >
                    {bot.avatar}
                  </div>
                  {typingBots.has(bot.id) && (
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800" />
                  )}
                </div>
              ))}
              {chatState.bots.length > 5 && (
                <div className="w-8 h-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center text-xs font-medium text-gray-600 dark:text-gray-300">
                  +{chatState.bots.length - 5}
                </div>
              )}
            </div>
          </div>
        </motion.div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          <AnimatePresence>
            {chatState.messages.map((message) => {
              const bot = message.type === 'bot' ? getBotById(message.sender) : undefined;
              return (
                <MessageBubble
                  key={message.id}
                  message={message}
                  botColor={bot?.color}
                  botAvatar={bot?.avatar}
                />
              );
            })}
          </AnimatePresence>

          {/* Typing Indicators */}
          <AnimatePresence>
            {Array.from(typingBots).map((botId) => {
              const bot = getBotById(botId);
              if (!bot) return null;
              
              return (
                <TypingIndicator
                  key={botId}
                  botName={bot.name}
                  avatar={bot.avatar}
                  color={bot.color}
                />
              );
            })}
          </AnimatePresence>

          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          className="p-4 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-t border-gray-200/50 dark:border-gray-700/50"
        >
          <UserInput
            onSendMessage={handleUserMessage}
            bots={chatState.bots}
            disabled={false}
          />
        </motion.div>
      </div>
    </div>
  );
}
