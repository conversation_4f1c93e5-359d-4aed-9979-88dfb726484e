'use client';

import { motion } from 'framer-motion';
import { Message } from '../types';

interface MessageBubbleProps {
  message: Message;
  botColor?: string;
  botAvatar?: string;
}

export default function MessageBubble({ message, botColor, botAvatar }: MessageBubbleProps) {
  const isUser = message.type === 'user';
  const timestamp = new Date(message.timestamp).toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  });

  // Highlight mentions in the message
  const highlightMentions = (content: string) => {
    return content.replace(/@(\w+)/g, '<span class="bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400 px-1 rounded">@$1</span>');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className={`flex gap-3 ${isUser ? 'justify-end' : 'justify-start'} group`}
    >
      {!isUser && (
        <div 
          className="w-8 h-8 rounded-full flex items-center justify-center text-white font-medium text-sm flex-shrink-0 mt-1"
          style={{ backgroundColor: botColor || '#3B82F6' }}
        >
          {botAvatar || '🤖'}
        </div>
      )}
      
      <div className={`flex flex-col max-w-[70%] ${isUser ? 'items-end' : 'items-start'}`}>
        <div className="flex items-center gap-2 mb-1">
          <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
            {message.senderName}
          </span>
          <span className="text-xs text-gray-400 dark:text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity">
            {timestamp}
          </span>
        </div>
        
        <motion.div
          whileHover={{ scale: 1.02 }}
          className={`
            px-4 py-3 rounded-2xl backdrop-blur-sm border
            ${isUser 
              ? 'bg-blue-500 text-white border-blue-400/50' 
              : 'bg-white/70 dark:bg-gray-800/70 text-gray-800 dark:text-gray-200 border-gray-200/50 dark:border-gray-700/50'
            }
            shadow-sm hover:shadow-md transition-all duration-200
          `}
        >
          <div 
            className="text-sm leading-relaxed"
            dangerouslySetInnerHTML={{ 
              __html: highlightMentions(message.content) 
            }}
          />
          
          {message.mentions && message.mentions.length > 0 && (
            <div className="mt-2 pt-2 border-t border-gray-200/30 dark:border-gray-600/30">
              <div className="flex flex-wrap gap-1">
                {message.mentions.map((mention, index) => (
                  <span 
                    key={index}
                    className="text-xs bg-blue-100/50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-2 py-1 rounded-full"
                  >
                    @{mention}
                  </span>
                ))}
              </div>
            </div>
          )}
        </motion.div>
      </div>
      
      {isUser && (
        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center text-white font-medium text-sm flex-shrink-0 mt-1">
          👤
        </div>
      )}
    </motion.div>
  );
}
