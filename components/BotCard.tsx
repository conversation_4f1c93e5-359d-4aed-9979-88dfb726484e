'use client';

import { motion } from 'framer-motion';
import { Trash2, MessageCircle } from 'lucide-react';
import { Bot } from '../types';

interface BotCardProps {
  bot: Bot;
  onDelete: (botId: string) => void;
  onMention: (botName: string) => void;
}

export default function BotCard({ bot, onDelete, onMention }: BotCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      whileHover={{ scale: 1.02 }}
      className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-xl p-4 border border-gray-200/50 dark:border-gray-700/50 shadow-sm hover:shadow-md transition-all duration-200"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-3">
          <div 
            className="w-10 h-10 rounded-full flex items-center justify-center text-white font-medium"
            style={{ backgroundColor: bot.color }}
          >
            {bot.avatar}
          </div>
          <div>
            <h3 className="font-semibold text-gray-800 dark:text-gray-200">
              {bot.name}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
              {bot.responseStyle} • {bot.model}
            </p>
          </div>
        </div>
        
        <div className="flex gap-1">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => onMention(bot.name)}
            className="p-1.5 text-gray-400 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/30 rounded-lg transition-colors"
            title={`Mention ${bot.name}`}
          >
            <MessageCircle size={14} />
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={() => onDelete(bot.id)}
            className="p-1.5 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg transition-colors"
            title={`Delete ${bot.name}`}
          >
            <Trash2 size={14} />
          </motion.button>
        </div>
      </div>
      
      <div className="space-y-2">
        <div>
          <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
            Knowledge Domain
          </p>
          <p className="text-sm text-gray-700 dark:text-gray-300 bg-gray-100/50 dark:bg-gray-700/50 rounded-lg px-2 py-1">
            {bot.knowledgeDomain}
          </p>
        </div>
        
        <div>
          <p className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
            Persona
          </p>
          <p className="text-sm text-gray-700 dark:text-gray-300 bg-gray-100/50 dark:bg-gray-700/50 rounded-lg px-2 py-1 line-clamp-2">
            {bot.persona}
          </p>
        </div>
      </div>
      
      {bot.isTyping && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mt-3 flex items-center gap-2 text-xs text-blue-500"
        >
          <div className="flex gap-1">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-1 h-1 bg-blue-500 rounded-full"
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2,
                }}
              />
            ))}
          </div>
          <span>Typing...</span>
        </motion.div>
      )}
    </motion.div>
  );
}
