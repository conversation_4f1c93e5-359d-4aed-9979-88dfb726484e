import { NextRequest, NextResponse } from 'next/server';
import { generateBotResponse } from '../../../utils/openai';
import { formatConversationForBot } from '../../../utils/chatLogic';
import { Bot, Message } from '../../../types';

export async function POST(request: NextRequest) {
  try {
    const { bot, messages } = await request.json();
    
    if (!bot || !messages) {
      return NextResponse.json(
        { error: 'Bot and messages are required' },
        { status: 400 }
      );
    }

    const conversationHistory = formatConversationForBot(messages, bot.id);
    
    const response = await generateBotResponse(
      bot.persona,
      bot.knowledgeDomain,
      conversationHistory,
      bot.model
    );

    return NextResponse.json({ response });
  } catch (error) {
    console.error('Chat API error:', error);
    return NextResponse.json(
      { error: 'Failed to generate response' },
      { status: 500 }
    );
  }
}
