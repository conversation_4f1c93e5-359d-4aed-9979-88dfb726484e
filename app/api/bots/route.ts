import { NextRequest, NextResponse } from 'next/server';
import { Bot } from '../../../types';

// In a real app, this would be stored in a database
let bots: Bot[] = [];

export async function GET() {
  return NextResponse.json({ bots });
}

export async function POST(request: NextRequest) {
  try {
    const botData = await request.json();
    
    const newBot: Bot = {
      id: `bot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: botData.name,
      model: botData.model || 'gpt-3.5-turbo',
      knowledgeDomain: botData.knowledgeDomain,
      persona: botData.persona,
      avatar: botData.avatar || '🤖',
      color: botData.color || '#3B82F6',
      isTyping: false,
      lastActiveTime: 0,
      responseStyle: botData.responseStyle || 'thoughtful'
    };

    bots.push(newBot);
    
    return NextResponse.json({ bot: newBot });
  } catch (error) {
    console.error('Bot creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create bot' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const botId = searchParams.get('id');
    
    if (!botId) {
      return NextResponse.json(
        { error: 'Bot ID is required' },
        { status: 400 }
      );
    }

    bots = bots.filter(bot => bot.id !== botId);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Bot deletion error:', error);
    return NextResponse.json(
      { error: 'Failed to delete bot' },
      { status: 500 }
    );
  }
}
